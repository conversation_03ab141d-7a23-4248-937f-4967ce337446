"""
Learning Service for the AI Companion System.
Handles user preference analysis, content curation, and adaptive learning.
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from collections import defaultdict, Counter
import schedule
import time
import threading

from models import (
    UserProfile, LearningInsight, MemoryEntry, EmotionType, 
    InteractionType, MemoryType, PersonalMemory, UniversalMemory
)
from memory_service import MemoryService
from gemini_service import GeminiService
from config import settings

class LearningService:
    """Learning service for adaptive AI companion behavior."""
    
    def __init__(self, memory_service: MemoryService, gemini_service: GeminiService):
        """Initialize the learning service."""
        self.memory_service = memory_service
        self.gemini_service = gemini_service
        self.learning_rate = settings.learning_rate
        self.adaptation_threshold = settings.adaptation_threshold
        
        # Learning state tracking
        self.user_learning_states = {}
        self.global_learning_insights = []
        
        # Start background learning tasks
        self._start_background_tasks()
    
    def analyze_user_preferences(
        self,
        user_id: str,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze user preferences from conversation history."""
        try:
            # Get user profile
            user_profile = self.memory_service.get_user_profile(user_id)
            if not user_profile:
                return {}
            
            # Analyze recent conversations for patterns
            preferences = self._extract_preference_patterns(conversation_history)
            
            # Analyze communication style
            communication_style = self._analyze_communication_style(conversation_history)
            
            # Analyze emotional patterns
            emotional_patterns = self._analyze_emotional_patterns(conversation_history)
            
            # Analyze topic interests
            topic_interests = self._analyze_topic_interests(conversation_history)
            
            # Analyze interaction timing patterns
            timing_patterns = self._analyze_timing_patterns(conversation_history)
            
            return {
                'preferences': preferences,
                'communication_style': communication_style,
                'emotional_patterns': emotional_patterns,
                'topic_interests': topic_interests,
                'timing_patterns': timing_patterns,
                'confidence_scores': self._calculate_confidence_scores(conversation_history)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def generate_learning_insights(
        self,
        user_id: str,
        recent_interactions: List[Dict[str, Any]]
    ) -> List[LearningInsight]:
        """Generate learning insights from recent user interactions."""
        insights = []
        
        try:
            # Analyze conversation patterns
            conversation_insights = self._analyze_conversation_patterns(recent_interactions)
            insights.extend(conversation_insights)
            
            # Analyze emotional trends
            emotional_insights = self._analyze_emotional_trends(recent_interactions)
            insights.extend(emotional_insights)
            
            # Analyze preference changes
            preference_insights = self._analyze_preference_changes(user_id, recent_interactions)
            insights.extend(preference_insights)
            
            # Analyze relationship dynamics
            relationship_insights = self._analyze_relationship_dynamics(user_id, recent_interactions)
            insights.extend(relationship_insights)
            
            # Store insights
            for insight in insights:
                self._store_learning_insight(insight)
            
            return insights
            
        except Exception as e:
            return []
    
    def curate_personalized_content(
        self,
        user_id: str,
        content_type: str = "conversation_starters"
    ) -> List[Dict[str, Any]]:
        """Curate personalized content based on user preferences."""
        try:
            user_profile = self.memory_service.get_user_profile(user_id)
            if not user_profile:
                return []
            
            # Get user preferences and interests
            preferences = user_profile.preferences
            interests = user_profile.interests
            emotional_patterns = user_profile.emotional_patterns
            
            # Get recent topics and memories
            recent_memories = self.memory_service.retrieve_relevant_memories(
                user_id, "", MemoryType.PERSONAL, limit=20
            )
            
            # Analyze current emotional state
            current_emotion = self._get_dominant_emotion(emotional_patterns)
            
            if content_type == "conversation_starters":
                return self._generate_conversation_starters(
                    user_profile, recent_memories, current_emotion
                )
            elif content_type == "activity_suggestions":
                return self._generate_activity_suggestions(
                    user_profile, recent_memories, current_emotion
                )
            elif content_type == "emotional_support":
                return self._generate_emotional_support_content(
                    user_profile, recent_memories, current_emotion
                )
            else:
                return []
                
        except Exception as e:
            return []
    
    def adapt_conversation_style(
        self,
        user_id: str,
        base_response: str,
        user_profile: UserProfile
    ) -> str:
        """Adapt conversation style based on user preferences."""
        try:
            communication_style = user_profile.communication_style
            
            # Analyze current response characteristics
            response_analysis = self._analyze_response_characteristics(base_response)
            
            # Determine adaptation needed
            adaptations = self._determine_style_adaptations(
                response_analysis, communication_style
            )
            
            # Apply adaptations
            adapted_response = self._apply_style_adaptations(base_response, adaptations)
            
            return adapted_response
            
        except Exception as e:
            return base_response
    
    def predict_user_needs(
        self,
        user_id: str,
        current_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Predict user needs based on patterns and current context."""
        try:
            # Get user profile and recent interactions
            user_profile = self.memory_service.get_user_profile(user_id)
            recent_memories = self.memory_service.retrieve_relevant_memories(
                user_id, "", MemoryType.PERSONAL, limit=50
            )
            
            # Analyze patterns
            patterns = self._analyze_user_patterns(recent_memories)
            
            # Predict immediate needs
            immediate_needs = self._predict_immediate_needs(
                user_profile, patterns, current_context
            )
            
            # Predict long-term needs
            long_term_needs = self._predict_long_term_needs(
                user_profile, patterns, current_context
            )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                immediate_needs, long_term_needs, user_profile
            )
            
            return {
                'immediate_needs': immediate_needs,
                'long_term_needs': long_term_needs,
                'recommendations': recommendations,
                'confidence': self._calculate_prediction_confidence(patterns)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _extract_preference_patterns(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract preference patterns from conversation history."""
        preferences = defaultdict(list)
        
        for msg in conversation_history:
            if msg.get('role') == 'user':
                content = msg.get('content', '').lower()
                
                # Extract explicit preferences
                if 'like' in content or 'love' in content or 'enjoy' in content:
                    # Extract what they like
                    pass
                
                if 'hate' in content or 'dislike' in content or 'don\'t like' in content:
                    # Extract what they dislike
                    pass
                
                # Extract implicit preferences from topics
                topics = self._extract_topics_from_text(content)
                for topic in topics:
                    preferences['topics'].append(topic)
        
        # Aggregate preferences
        return {
            'explicit_likes': list(set(preferences.get('likes', []))),
            'explicit_dislikes': list(set(preferences.get('dislikes', []))),
            'topic_preferences': Counter(preferences.get('topics', [])).most_common(10),
            'communication_preferences': self._extract_communication_preferences(conversation_history)
        }
    
    def _analyze_communication_style(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Analyze user's communication style preferences."""
        style_indicators = {
            'formality': 0.0,
            'humor': 0.0,
            'empathy': 0.0,
            'directness': 0.0,
            'detail_level': 0.0
        }
        
        user_messages = [msg for msg in conversation_history if msg.get('role') == 'user']
        
        for msg in user_messages:
            content = msg.get('content', '')
            
            # Analyze formality
            formal_words = ['indeed', 'furthermore', 'consequently', 'therefore']
            informal_words = ['hey', 'cool', 'awesome', 'yeah']
            
            formal_count = sum(1 for word in formal_words if word in content.lower())
            informal_count = sum(1 for word in informal_words if word in content.lower())
            
            if formal_count > informal_count:
                style_indicators['formality'] += 0.1
            elif informal_count > formal_count:
                style_indicators['formality'] -= 0.1
            
            # Analyze humor
            if any(word in content.lower() for word in ['haha', 'lol', 'funny', 'joke']):
                style_indicators['humor'] += 0.1
            
            # Analyze empathy
            if any(word in content.lower() for word in ['feel', 'understand', 'care', 'sorry']):
                style_indicators['empathy'] += 0.1
        
        # Normalize scores
        for key in style_indicators:
            style_indicators[key] = max(-1.0, min(1.0, style_indicators[key]))
        
        return style_indicators
    
    def _analyze_emotional_patterns(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[EmotionType, float]:
        """Analyze emotional patterns in conversation history."""
        emotion_counts = defaultdict(int)
        total_messages = 0
        
        for msg in conversation_history:
            if msg.get('role') == 'user':
                total_messages += 1
                emotion = msg.get('emotion')
                if emotion:
                    emotion_counts[emotion] += 1
        
        # Calculate emotion frequencies
        emotional_patterns = {}
        for emotion in EmotionType:
            frequency = emotion_counts[emotion] / max(total_messages, 1)
            emotional_patterns[emotion] = frequency
        
        return emotional_patterns
    
    def _analyze_topic_interests(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> List[str]:
        """Analyze topic interests from conversation history."""
        topics = []
        
        for msg in conversation_history:
            if msg.get('role') == 'user':
                content = msg.get('content', '')
                extracted_topics = self._extract_topics_from_text(content)
                topics.extend(extracted_topics)
        
        # Return most common topics
        return [topic for topic, count in Counter(topics).most_common(10)]
    
    def _analyze_timing_patterns(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze timing patterns in user interactions."""
        if len(conversation_history) < 2:
            return {}
        
        timestamps = []
        for msg in conversation_history:
            if msg.get('role') == 'user':
                timestamp = msg.get('timestamp')
                if timestamp:
                    if isinstance(timestamp, str):
                        timestamp = datetime.fromisoformat(timestamp)
                    timestamps.append(timestamp)
        
        if len(timestamps) < 2:
            return {}
        
        # Calculate time differences
        time_diffs = []
        for i in range(1, len(timestamps)):
            diff = timestamps[i] - timestamps[i-1]
            time_diffs.append(diff.total_seconds())
        
        # Analyze patterns
        avg_response_time = np.mean(time_diffs) if time_diffs else 0
        std_response_time = np.std(time_diffs) if time_diffs else 0
        
        # Analyze time of day patterns
        hour_counts = Counter([ts.hour for ts in timestamps])
        peak_hours = [hour for hour, count in hour_counts.most_common(3)]
        
        return {
            'average_response_time': avg_response_time,
            'response_time_variability': std_response_time,
            'peak_activity_hours': peak_hours,
            'total_interactions': len(timestamps)
        }
    
    def _extract_topics_from_text(self, text: str) -> List[str]:
        """Extract topics from text using simple keyword matching."""
        # This is a simplified topic extraction
        # In a production system, you'd use more sophisticated NLP
        topics = []
        
        # Define topic keywords
        topic_keywords = {
            'technology': ['computer', 'phone', 'app', 'software', 'tech', 'programming'],
            'health': ['exercise', 'workout', 'diet', 'health', 'fitness', 'wellness'],
            'work': ['job', 'work', 'career', 'office', 'meeting', 'project'],
            'family': ['family', 'kids', 'parents', 'siblings', 'home'],
            'hobbies': ['hobby', 'interest', 'passion', 'activity', 'fun'],
            'travel': ['travel', 'vacation', 'trip', 'destination', 'adventure'],
            'music': ['music', 'song', 'artist', 'concert', 'playlist'],
            'movies': ['movie', 'film', 'show', 'series', 'entertainment'],
            'books': ['book', 'reading', 'author', 'novel', 'literature'],
            'food': ['food', 'cooking', 'recipe', 'restaurant', 'cuisine']
        }
        
        text_lower = text.lower()
        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                topics.append(topic)
        
        return topics
    
    def _calculate_confidence_scores(
        self,
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Calculate confidence scores for different analyses."""
        total_messages = len([msg for msg in conversation_history if msg.get('role') == 'user'])
        
        return {
            'preference_confidence': min(1.0, total_messages / 20),
            'style_confidence': min(1.0, total_messages / 15),
            'emotional_confidence': min(1.0, total_messages / 10),
            'topic_confidence': min(1.0, total_messages / 25)
        }
    
    def _generate_conversation_starters(
        self,
        user_profile: UserProfile,
        recent_memories: List[MemoryEntry],
        current_emotion: EmotionType
    ) -> List[Dict[str, Any]]:
        """Generate personalized conversation starters."""
        starters = []
        
        # Use Gemini to generate conversation starters
        try:
            topics = self.gemini_service.suggest_conversation_topics(
                user_profile.dict(),
                [memory.content for memory in recent_memories[:5]],
                user_profile.interests
            )
            
            for topic in topics:
                starters.append({
                    'type': 'conversation_starter',
                    'content': topic,
                    'relevance_score': 0.8,
                    'emotional_context': current_emotion.value
                })
                
        except Exception as e:
            # Fallback conversation starters
            starters = [
                {
                    'type': 'conversation_starter',
                    'content': 'How has your day been so far?',
                    'relevance_score': 0.6,
                    'emotional_context': 'neutral'
                },
                {
                    'type': 'conversation_starter',
                    'content': 'Is there anything on your mind that you\'d like to talk about?',
                    'relevance_score': 0.7,
                    'emotional_context': 'supportive'
                }
            ]
        
        return starters
    
    def _generate_activity_suggestions(
        self,
        user_profile: UserProfile,
        recent_memories: List[MemoryEntry],
        current_emotion: EmotionType
    ) -> List[Dict[str, Any]]:
        """Generate personalized activity suggestions."""
        activities = []
        
        # Base activities on user interests and current emotion
        if current_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY]:
            activities.extend([
                {
                    'type': 'activity_suggestion',
                    'content': 'Would you like to take a short walk? Sometimes fresh air helps clear the mind.',
                    'relevance_score': 0.8,
                    'emotional_context': 'supportive'
                },
                {
                    'type': 'activity_suggestion',
                    'content': 'How about listening to some calming music?',
                    'relevance_score': 0.7,
                    'emotional_context': 'calming'
                }
            ])
        
        elif current_emotion in [EmotionType.JOY, EmotionType.EXCITEMENT]:
            activities.extend([
                {
                    'type': 'activity_suggestion',
                    'content': 'This is great energy! Any plans to celebrate or share this moment?',
                    'relevance_score': 0.8,
                    'emotional_context': 'celebratory'
                }
            ])
        
        # Add interest-based activities
        for interest in user_profile.interests[:3]:
            activities.append({
                'type': 'activity_suggestion',
                'content': f'Have you had a chance to work on your {interest} lately?',
                'relevance_score': 0.9,
                'emotional_context': 'interest_based'
            })
        
        return activities
    
    def _generate_emotional_support_content(
        self,
        user_profile: UserProfile,
        recent_memories: List[MemoryEntry],
        current_emotion: EmotionType
    ) -> List[Dict[str, Any]]:
        """Generate emotional support content."""
        support_content = []
        
        if current_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY, EmotionType.FEAR]:
            support_content.extend([
                {
                    'type': 'emotional_support',
                    'content': 'I\'m here to listen. Whatever you\'re going through, you don\'t have to face it alone.',
                    'relevance_score': 0.9,
                    'emotional_context': 'supportive'
                },
                {
                    'type': 'emotional_support',
                    'content': 'It\'s okay to not be okay. Take your time, and remember that this feeling won\'t last forever.',
                    'relevance_score': 0.8,
                    'emotional_context': 'validating'
                }
            ])
        
        return support_content
    
    def _get_dominant_emotion(self, emotional_patterns: Dict[EmotionType, float]) -> EmotionType:
        """Get the dominant emotion from emotional patterns."""
        if not emotional_patterns:
            return EmotionType.NEUTRAL
        
        dominant_emotion = max(emotional_patterns.items(), key=lambda x: x[1])
        return dominant_emotion[0]
    
    def _store_learning_insight(self, insight: LearningInsight):
        """Store a learning insight."""
        # In a production system, you'd store this in a database
        # For now, we'll just keep it in memory
        self.global_learning_insights.append(insight)
    
    def _start_background_tasks(self):
        """Start background learning tasks."""
        def run_background_tasks():
            schedule.every(1).hours.do(self._periodic_learning_analysis)
            schedule.every(6).hours.do(self._cleanup_old_insights)
            
            while True:
                schedule.run_pending()
                time.sleep(60)
        
        # Start background thread
        background_thread = threading.Thread(target=run_background_tasks, daemon=True)
        background_thread.start()
    
    def _periodic_learning_analysis(self):
        """Periodic analysis of learning patterns."""
        # This would analyze patterns across all users
        # and update global learning insights
        pass
    
    def _cleanup_old_insights(self):
        """Clean up old learning insights."""
        # Remove insights older than 30 days
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        self.global_learning_insights = [
            insight for insight in self.global_learning_insights
            if insight.created_at > cutoff_date
        ] 