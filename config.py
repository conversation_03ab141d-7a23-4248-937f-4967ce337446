"""
Configuration module for the AI Companion System.
Handles environment variables, API keys, and system settings.
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseSettings, Field

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Main settings class for the AI Companion System."""
    
    # API Keys
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    
    # Database Configuration
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    database_url: str = Field("sqlite:///./ai_companion.db", env="DATABASE_URL")
    
    # Memory Configuration
    memory_ttl: int = Field(2592000, env="MEMORY_TTL")  # 30 days in seconds
    personal_memory_size: int = Field(1000, env="PERSONAL_MEMORY_SIZE")
    universal_memory_size: int = Field(10000, env="UNIVERSAL_MEMORY_SIZE")
    
    # Learning Configuration
    learning_rate: float = Field(0.1, env="LEARNING_RATE")
    adaptation_threshold: int = Field(5, env="ADAPTATION_THRESHOLD")
    emotion_weight: float = Field(0.3, env="EMOTION_WEIGHT")
    
    # Conversation Configuration
    max_context_length: int = Field(2000, env="MAX_CONTEXT_LENGTH")
    max_history_length: int = Field(50, env="MAX_HISTORY_LENGTH")
    response_temperature: float = Field(0.7, env="RESPONSE_TEMPERATURE")
    max_tokens: int = Field(150, env="MAX_TOKENS")
    
    # System Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    environment: str = Field("development", env="ENVIRONMENT")
    debug_mode: bool = Field(True, env="DEBUG_MODE")
    
    # Service Ports
    gradio_port: int = Field(7860, env="GRADIO_PORT")
    api_port: int = Field(8000, env="API_PORT")
    memory_service_port: int = Field(8001, env="MEMORY_SERVICE_PORT")
    learning_service_port: int = Field(8002, env="LEARNING_SERVICE_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

# Validation
def validate_settings():
    """Validate critical settings and provide helpful error messages."""
    if not settings.gemini_api_key:
        raise ValueError("GEMINI_API_KEY is required. Please set it in your .env file.")
    
    if settings.learning_rate <= 0 or settings.learning_rate > 1:
        raise ValueError("LEARNING_RATE must be between 0 and 1")
    
    if settings.emotion_weight < 0 or settings.emotion_weight > 1:
        raise ValueError("EMOTION_WEIGHT must be between 0 and 1")
    
    if settings.response_temperature < 0 or settings.response_temperature > 2:
        raise ValueError("RESPONSE_TEMPERATURE must be between 0 and 2")

# Validate on import
try:
    validate_settings()
except ValueError as e:
    print(f"Configuration Error: {e}")
    print("Please check your .env file and ensure all required settings are correct.")
    raise 